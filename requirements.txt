# Robust Data Storage Service Requirements
# Comprehensive dependencies for high-frequency market data management

# Core API Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and TimescaleDB
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1

# Data Processing and Analysis
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# Fyers API Integration
fyers-apiv3==3.0.6

# Configuration Management
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging and Monitoring
structlog==23.2.0

# HTTP and Networking
requests==2.31.0
httpx==0.25.2

# Date and Time Utilities
python-dateutil==2.8.2
pytz==2023.3

# Data Validation
marshmallow==3.20.1

# Caching
cachetools==5.3.2

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0

# Production Deployment
gunicorn==21.2.0

# Monitoring and Health Checks
psutil==5.9.6

# Security
cryptography==41.0.7