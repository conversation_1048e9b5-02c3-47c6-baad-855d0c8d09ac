#!/usr/bin/env python3
"""
Test data insertion into the new OHLCV tables.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from database.connection import get_db
from database.models import EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV, OptionType
from sqlalchemy.orm import Session

def test_data_insertion():
    """Test inserting sample data into all OHLCV tables."""
    db: Session = next(get_db())
    
    try:
        print("Testing data insertion into OHLCV tables...")
        
        # Test data for equity
        equity_data = EquityOHLCV(
            symbol="RELIANCE",
            exchange="NSE",
            interval="1m",
            datetime=datetime.now() - timedelta(minutes=1),
            open=2500.0,
            high=2510.0,
            low=2495.0,
            close=2505.0,
            volume=1000
        )
        
        db.add(equity_data)
        print("✓ Added equity test data")
        
        # Test data for index
        index_data = IndexOHLCV(
            symbol="NIFTY50",
            exchange="NSE",
            interval="1m",
            datetime=datetime.now() - timedelta(minutes=1),
            open=19500.0,
            high=19520.0,
            low=19480.0,
            close=19510.0,
            volume=0  # Indices typically have 0 volume
        )
        
        db.add(index_data)
        print("✓ Added index test data")
        
        # Test data for futures
        futures_data = FuturesOHLCV(
            symbol="RELIANCE",
            exchange="NSE",
            interval="1m",
            datetime=datetime.now() - timedelta(minutes=1),
            open=2500.0,
            high=2510.0,
            low=2495.0,
            close=2505.0,
            volume=500,
            expiry_date=datetime(2025, 1, 30).date(),
            open_interest=10000
        )
        
        db.add(futures_data)
        print("✓ Added futures test data")
        
        # Test data for options
        options_data = OptionsOHLCV(
            symbol="RELIANCE",
            exchange="NSE",
            interval="1m",
            datetime=datetime.now() - timedelta(minutes=1),
            open=50.0,
            high=52.0,
            low=48.0,
            close=51.0,
            volume=200,
            expiry_date=datetime(2025, 1, 30).date(),
            strike_price=2500.0,
            option_type=OptionType.CE,
            open_interest=5000
        )
        
        db.add(options_data)
        print("✓ Added options test data")
        
        # Commit all changes
        db.commit()
        print("\n✅ All test data inserted successfully!")
        
        # Verify data
        print("\nVerifying inserted data:")
        
        equity_count = db.query(EquityOHLCV).count()
        index_count = db.query(IndexOHLCV).count()
        futures_count = db.query(FuturesOHLCV).count()
        options_count = db.query(OptionsOHLCV).count()
        
        print(f"  Equity records: {equity_count}")
        print(f"  Index records: {index_count}")
        print(f"  Futures records: {futures_count}")
        print(f"  Options records: {options_count}")
        
        # Show sample data
        print("\nSample records:")
        
        equity_sample = db.query(EquityOHLCV).first()
        if equity_sample:
            print(f"  Equity: {equity_sample.symbol} - {equity_sample.close} @ {equity_sample.datetime}")
        
        index_sample = db.query(IndexOHLCV).first()
        if index_sample:
            print(f"  Index: {index_sample.symbol} - {index_sample.close} @ {index_sample.datetime}")
        
        futures_sample = db.query(FuturesOHLCV).first()
        if futures_sample:
            print(f"  Futures: {futures_sample.symbol} - {futures_sample.close} @ {futures_sample.datetime} (Expiry: {futures_sample.expiry_date})")
        
        options_sample = db.query(OptionsOHLCV).first()
        if options_sample:
            print(f"  Options: {options_sample.symbol} - {options_sample.close} @ {options_sample.datetime} (Strike: {options_sample.strike_price}, Type: {options_sample.option_type})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    test_data_insertion()
