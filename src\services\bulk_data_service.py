"""
Bulk Data Population Service for all market types.
Handles real historical data fetching and population from Fyers API.
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import and_

from database.connection import get_db
from database.models import MarketType, EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV
from services.fyers_auth_service import FyersAuthService
from core.config import settings

logger = logging.getLogger(__name__)


class BulkDataService:
    """Service for bulk data population across all market types."""
    
    def __init__(self):
        """Initialize the bulk data service."""
        self.fyers_auth = FyersAuthService()
        self.db: Session = next(get_db())
        
        # Market type to model mapping
        self.model_map = {
            MarketType.EQUITY: EquityOHLCV,
            MarketType.INDEX: IndexOHLCV,
            MarketType.FUTURES: FuturesOHLCV,
            MarketType.OPTIONS: OptionsOHLCV
        }
    
    def __del__(self):
        """Clean up database connection."""
        if hasattr(self, 'db'):
            self.db.close()
    
    async def populate_historical_data(
        self,
        symbols: List[str],
        market_type: MarketType,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1m",
        exchange: str = "NSE"
    ) -> Dict[str, bool]:
        """
        Populate historical data for multiple symbols.
        
        Args:
            symbols: List of symbols to fetch data for
            market_type: Type of market (EQUITY, INDEX, FUTURES, OPTIONS)
            start_date: Start date for data fetching
            end_date: End date for data fetching
            interval: Data interval (default: 1m)
            exchange: Exchange name (default: NSE)
            
        Returns:
            Dictionary mapping symbol to success status
        """
        if not self.fyers_auth.is_authenticated():
            logger.error("Fyers service not authenticated")
            return {symbol: False for symbol in symbols}
        
        results = {}
        total_symbols = len(symbols)
        
        logger.info(f"Starting bulk data population for {total_symbols} {market_type.value} symbols")
        
        for i, symbol in enumerate(symbols, 1):
            logger.info(f"Processing {symbol} ({i}/{total_symbols})")
            
            try:
                # Convert symbol to Fyers format
                fyers_symbol = self._convert_to_fyers_symbol(symbol, market_type)
                
                # Fetch historical data from Fyers
                historical_data = self.fyers_auth.fetch_historical_data_chunked(
                    symbol=fyers_symbol,
                    start_date=start_date,
                    end_date=end_date,
                    interval=1  # 1-minute data
                )
                
                if not historical_data:
                    logger.warning(f"No data received for {symbol}")
                    results[symbol] = False
                    continue
                
                # Convert to database format
                db_records = self._convert_to_db_format(
                    historical_data, symbol, market_type, interval, exchange
                )
                
                # Bulk insert to database
                success = self._bulk_insert_data(db_records, market_type)
                results[symbol] = success
                
                if success:
                    logger.info(f"✅ Successfully populated {len(db_records)} records for {symbol}")
                else:
                    logger.error(f"❌ Failed to populate data for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                results[symbol] = False
        
        # Summary
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Bulk population completed: {successful}/{total_symbols} symbols successful")
        
        return results
    
    def _convert_to_fyers_symbol(self, symbol: str, market_type: MarketType) -> str:
        """Convert symbol to Fyers format based on market type."""
        if market_type == MarketType.EQUITY:
            return f"NSE:{symbol}-EQ"
        elif market_type == MarketType.INDEX:
            return f"NSE:{symbol}-INDEX"
        elif market_type == MarketType.FUTURES:
            return f"NSE:{symbol}-FUT"
        elif market_type == MarketType.OPTIONS:
            return f"NSE:{symbol}-OPT"
        else:
            return f"NSE:{symbol}-EQ"  # Default to equity
    
    def _convert_to_db_format(
        self,
        historical_data: List[Dict],
        symbol: str,
        market_type: MarketType,
        interval: str,
        exchange: str
    ) -> List[Dict]:
        """Convert Fyers data format to database format."""
        db_records = []
        
        for record in historical_data:
            base_record = {
                'symbol': symbol,
                'exchange': exchange,
                'interval': interval,
                'datetime': datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S'),
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': int(record['volume'])
            }
            
            # Add market-specific fields for futures and options
            if market_type in [MarketType.FUTURES, MarketType.OPTIONS]:
                # For now, using placeholder values - these should come from symbol parsing
                base_record['expiry_date'] = datetime.now().date()  # Placeholder
                base_record['open_interest'] = 0
                
                if market_type == MarketType.OPTIONS:
                    base_record['strike_price'] = 0.0  # Placeholder
                    base_record['option_type'] = 'CE'  # Placeholder
            
            db_records.append(base_record)
        
        return db_records
    
    def _bulk_insert_data(self, records: List[Dict], market_type: MarketType) -> bool:
        """Bulk insert data into the appropriate table."""
        if not records:
            return True
        
        try:
            model_class = self.model_map[market_type]
            
            # Use PostgreSQL UPSERT (ON CONFLICT DO UPDATE)
            stmt = insert(model_class).values(records)
            
            # Define conflict resolution based on primary key
            if market_type in [MarketType.EQUITY, MarketType.INDEX]:
                stmt = stmt.on_conflict_do_update(
                    index_elements=['symbol', 'interval', 'datetime'],
                    set_=dict(
                        open=stmt.excluded.open,
                        high=stmt.excluded.high,
                        low=stmt.excluded.low,
                        close=stmt.excluded.close,
                        volume=stmt.excluded.volume
                    )
                )
            elif market_type == MarketType.FUTURES:
                stmt = stmt.on_conflict_do_update(
                    index_elements=['symbol', 'interval', 'datetime', 'expiry_date'],
                    set_=dict(
                        open=stmt.excluded.open,
                        high=stmt.excluded.high,
                        low=stmt.excluded.low,
                        close=stmt.excluded.close,
                        volume=stmt.excluded.volume,
                        open_interest=stmt.excluded.open_interest
                    )
                )
            elif market_type == MarketType.OPTIONS:
                stmt = stmt.on_conflict_do_update(
                    index_elements=['symbol', 'interval', 'datetime', 'expiry_date', 'strike_price', 'option_type'],
                    set_=dict(
                        open=stmt.excluded.open,
                        high=stmt.excluded.high,
                        low=stmt.excluded.low,
                        close=stmt.excluded.close,
                        volume=stmt.excluded.volume,
                        open_interest=stmt.excluded.open_interest
                    )
                )
            
            self.db.execute(stmt)
            self.db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error bulk inserting data: {e}")
            self.db.rollback()
            return False
    
    def get_data_summary(self, market_type: MarketType, symbols: List[str] = None) -> Dict[str, Any]:
        """Get data availability summary for symbols."""
        try:
            model_class = self.model_map[market_type]
            
            query = self.db.query(model_class)
            if symbols:
                query = query.filter(model_class.symbol.in_(symbols))
            
            # Get basic statistics
            total_records = query.count()
            
            if total_records == 0:
                return {
                    'total_records': 0,
                    'symbols_count': 0,
                    'date_range': None
                }
            
            # Get date range
            min_date = query.order_by(model_class.datetime.asc()).first().datetime
            max_date = query.order_by(model_class.datetime.desc()).first().datetime
            
            # Get unique symbols count
            symbols_count = query.distinct(model_class.symbol).count()
            
            return {
                'total_records': total_records,
                'symbols_count': symbols_count,
                'date_range': {
                    'start': min_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'end': max_date.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting data summary: {e}")
            return {'error': str(e)}
