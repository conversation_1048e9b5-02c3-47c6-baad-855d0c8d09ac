#!/usr/bin/env python3
"""
Test script for loading sample data directly into the database.
Tests data loading for 1 stock, 1 index, 1 futures, and 1 options contract.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from core.config import settings, validate_configuration
from core.logging import get_logger
from database.connection import get_db, check_database_connection, init_database
from database.models import MarketType, EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV, OptionType
from services.symbol_service import SymbolService
from services.historical_data_service import HistoricalDataService
from services.fyers_auth_service import FyersAuthService
from services.data_storage_service import DataStorageService
from sqlalchemy.orm import Session

logger = get_logger(__name__)

def test_database_connection():
    """Test database connection and schema."""
    logger.info("🔍 Testing database connection...")
    
    if not check_database_connection():
        logger.error("❌ Database connection failed")
        return False
    
    logger.info("✅ Database connection successful")
    return True

def test_hypertables():
    """Test TimescaleDB hypertables."""
    logger.info("🔍 Testing TimescaleDB hypertables...")
    
    try:
        db = next(get_db())
        
        # Check hypertables
        from sqlalchemy import text
        result = db.execute(text("""
            SELECT hypertable_name, num_chunks
            FROM timescaledb_information.hypertables
            WHERE hypertable_name IN ('equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv')
        """))
        
        hypertables = result.fetchall()
        logger.info(f"✅ Found {len(hypertables)} market-specific hypertables:")
        for table in hypertables:
            logger.info(f"   - {table[0]}: {table[1]} chunks")
        
        db.close()
        return len(hypertables) == 4
        
    except Exception as e:
        logger.error(f"❌ Hypertable test failed: {e}")
        return False

def insert_sample_data():
    """Insert sample OHLCV data for testing."""
    logger.info("📊 Inserting sample data...")
    
    try:
        db = next(get_db())
        storage_service = DataStorageService(db)
        
        # Sample timestamp
        base_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
        
        # 1. Sample Equity Data (RELIANCE)
        equity_data = []
        for i in range(5):
            equity_data.append({
                'datetime': base_time + timedelta(minutes=i),
                'symbol': 'RELIANCE',
                'open': 2500.0 + i,
                'high': 2510.0 + i,
                'low': 2495.0 + i,
                'close': 2505.0 + i,
                'volume': 100000 + i * 1000
            })
        
        # Insert equity data
        inserted, skipped = storage_service.bulk_insert_ohlcv(equity_data, MarketType.EQUITY)
        logger.info(f"✅ Equity (RELIANCE): Inserted {inserted}, Skipped {skipped}")
        
        # 2. Sample Index Data (NIFTY50)
        index_data = []
        for i in range(5):
            index_data.append({
                'datetime': base_time + timedelta(minutes=i),
                'symbol': 'NIFTY50',
                'open': 18000.0 + i * 10,
                'high': 18050.0 + i * 10,
                'low': 17950.0 + i * 10,
                'close': 18025.0 + i * 10,
                'volume': 0  # Indices typically don't have volume
            })
        
        # Insert index data
        inserted, skipped = storage_service.bulk_insert_ohlcv(index_data, MarketType.INDEX)
        logger.info(f"✅ Index (NIFTY50): Inserted {inserted}, Skipped {skipped}")
        
        # 3. Sample Futures Data (RELIANCE futures)
        futures_data = []
        expiry_date = (datetime.now() + timedelta(days=30)).date()
        for i in range(5):
            futures_data.append({
                'datetime': base_time + timedelta(minutes=i),
                'symbol': 'RELIANCE',
                'expiry_date': expiry_date,
                'open': 2520.0 + i,
                'high': 2530.0 + i,
                'low': 2515.0 + i,
                'close': 2525.0 + i,
                'volume': 50000 + i * 500,
                'open_interest': 1000000 + i * 1000
            })
        
        # Insert futures data
        inserted, skipped = storage_service.bulk_insert_ohlcv(futures_data, MarketType.FUTURES)
        logger.info(f"✅ Futures (RELIANCE): Inserted {inserted}, Skipped {skipped}")
        
        # 4. Sample Options Data (RELIANCE options)
        options_data = []
        strike_price = 2500.0
        for i in range(5):
            # Call option
            options_data.append({
                'datetime': base_time + timedelta(minutes=i),
                'symbol': 'RELIANCE',
                'expiry_date': expiry_date,
                'strike_price': strike_price,
                'option_type': 'CE',
                'open': 50.0 + i,
                'high': 55.0 + i,
                'low': 48.0 + i,
                'close': 52.0 + i,
                'volume': 10000 + i * 100,
                'open_interest': 500000 + i * 500
            })
            
            # Put option
            options_data.append({
                'datetime': base_time + timedelta(minutes=i),
                'symbol': 'RELIANCE',
                'expiry_date': expiry_date,
                'strike_price': strike_price,
                'option_type': 'PE',
                'open': 45.0 + i,
                'high': 50.0 + i,
                'low': 43.0 + i,
                'close': 47.0 + i,
                'volume': 8000 + i * 80,
                'open_interest': 400000 + i * 400
            })
        
        # Insert options data
        inserted, skipped = storage_service.bulk_insert_ohlcv(options_data, MarketType.OPTIONS)
        logger.info(f"✅ Options (RELIANCE): Inserted {inserted}, Skipped {skipped}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Sample data insertion failed: {e}")
        return False

def verify_data():
    """Verify inserted data."""
    logger.info("🔍 Verifying inserted data...")
    
    try:
        db = next(get_db())
        storage_service = DataStorageService(db)
        
        # Check equity data
        equity_summary = storage_service.get_data_summary('RELIANCE', MarketType.EQUITY)
        if equity_summary:
            logger.info(f"✅ Equity data: {equity_summary['total_records']} records")
            
            # Get latest records
            latest_equity = storage_service.get_latest_data('RELIANCE', MarketType.EQUITY, 3)
            logger.info(f"   Latest equity records: {len(latest_equity)}")
            for record in latest_equity[-2:]:
                logger.info(f"      {record['datetime']}: Close={record['close']}, Volume={record['volume']:,}")
        
        # Check index data
        index_summary = storage_service.get_data_summary('NIFTY50', MarketType.INDEX)
        if index_summary:
            logger.info(f"✅ Index data: {index_summary['total_records']} records")
            
            # Get latest records
            latest_index = storage_service.get_latest_data('NIFTY50', MarketType.INDEX, 3)
            logger.info(f"   Latest index records: {len(latest_index)}")
            for record in latest_index[-2:]:
                logger.info(f"      {record['datetime']}: Close={record['close']}")
        
        # Check futures data
        futures_summary = storage_service.get_data_summary('RELIANCE', MarketType.FUTURES)
        if futures_summary:
            logger.info(f"✅ Futures data: {futures_summary['total_records']} records")
            
            # Get latest records
            latest_futures = storage_service.get_latest_data('RELIANCE', MarketType.FUTURES, 3)
            logger.info(f"   Latest futures records: {len(latest_futures)}")
            for record in latest_futures[-2:]:
                logger.info(f"      {record['datetime']}: Close={record['close']}, OI={record.get('open_interest', 0):,}")
        
        # Check options data
        options_summary = storage_service.get_data_summary('RELIANCE', MarketType.OPTIONS)
        if options_summary:
            logger.info(f"✅ Options data: {options_summary['total_records']} records")
            
            # Get latest records
            latest_options = storage_service.get_latest_data('RELIANCE', MarketType.OPTIONS, 4)
            logger.info(f"   Latest options records: {len(latest_options)}")
            for record in latest_options[-2:]:
                logger.info(f"      {record['datetime']}: Strike={record.get('strike_price', 0)}, Type={record.get('option_type', '')}, Close={record['close']}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Data verification failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 Testing Data Loading for Market Segments")
    logger.info("=" * 80)
    
    # Validate configuration
    if not validate_configuration():
        logger.error("❌ Configuration validation failed")
        return False
    
    # Test database connection
    if not test_database_connection():
        return False
    
    # Test hypertables
    if not test_hypertables():
        return False
    
    # Insert sample data
    if not insert_sample_data():
        return False
    
    # Verify data
    if not verify_data():
        return False
    
    logger.info("\n" + "=" * 80)
    logger.info("✅ ALL TESTS PASSED!")
    logger.info("✅ Database schema is optimized for different market segments")
    logger.info("✅ Sample data loaded successfully for:")
    logger.info("   - 1 Stock (RELIANCE) in equity_ohlcv table")
    logger.info("   - 1 Index (NIFTY50) in index_ohlcv table") 
    logger.info("   - 1 Futures contract (RELIANCE) in futures_ohlcv table")
    logger.info("   - 1 Options contract (RELIANCE CE/PE) in options_ohlcv table")
    logger.info("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
