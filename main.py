#!/usr/bin/env python3
"""
Robust Data Storage Service - Main Entry Point
API-based approach for all operations (insert, delete, view, select) with clean separation of concerns.
"""

import sys
import os
import argparse
import signal
import time
import requests
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Any

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from core.config import settings, validate_configuration
from core.logging import get_logger
from database.connection import get_db, check_database_connection, init_database
from services.symbol_service import SymbolService
from services.historical_data_service import HistoricalDataService
from services.fyers_auth_service import FyersAuthService
from services.bulk_data_service import BulkDataService
from database.models import MarketType

logger = get_logger(__name__)

class DataServiceClient:
    """
    API-based client for the Robust Data Storage Service.
    Provides clean interface for all data operations through REST API.
    """

    def __init__(self, api_base_url: str = None):
        """Initialize the API client."""
        self.api_base_url = api_base_url or f"http://{settings.api.host}:{settings.api.port}"
        self.session = requests.Session()
        self.interrupted = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle interrupt signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.interrupted = True

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request to API."""
        url = f"{self.api_base_url}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            raise

    def health_check(self) -> Dict[str, Any]:
        """Check API health status."""
        return self._make_request("GET", "/health")

    def get_symbols(self, market_type: str = None, search: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get symbols from API."""
        params = {"limit": limit}
        if market_type:
            params["market_type"] = market_type
        if search:
            params["search"] = search

        response = self._make_request("GET", "/api/v1/symbols/", params=params)
        return response.get("symbols", [])

    def fetch_nse_symbols(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Fetch NSE symbols from CSV files."""
        params = {"force_refresh": force_refresh}
        return self._make_request("POST", "/api/v1/symbols/fetch", params=params)

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get detailed symbol information."""
        return self._make_request("GET", f"/api/v1/symbols/{symbol}")

    def get_latest_data(self, symbol: str, market_type: str, count: int = 100) -> Dict[str, Any]:
        """Get latest OHLCV data for a symbol."""
        params = {"market_type": market_type, "count": count}
        return self._make_request("GET", f"/api/v1/data/{symbol}/latest", params=params)

    def get_data_range(self, symbol: str, market_type: str, start_date: datetime,
                      end_date: datetime, limit: int = None) -> Dict[str, Any]:
        """Get OHLCV data for a date range."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        if limit:
            params["limit"] = limit

        return self._make_request("GET", f"/api/v1/data/{symbol}/range", params=params)

    def get_data_summary(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """Get data summary for a symbol."""
        params = {"market_type": market_type}
        return self._make_request("GET", f"/api/v1/data/{symbol}/summary", params=params)

    def delete_data_range(self, symbol: str, market_type: str, start_date: datetime,
                         end_date: datetime) -> Dict[str, Any]:
        """Delete OHLCV data for a date range."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        return self._make_request("DELETE", f"/api/v1/data/{symbol}/range", params=params)

    def fetch_historical_data(self, symbol: str, market_type: str, start_date: datetime,
                            end_date: datetime, resume: bool = True) -> Dict[str, Any]:
        """Fetch historical data for a symbol."""
        data = {
            "symbol": symbol,
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "resume": resume
        }
        return self._make_request("POST", "/api/v1/historical/fetch", json=data)

    def get_historical_status(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """Get historical data fetch status."""
        params = {"market_type": market_type}
        return self._make_request("GET", f"/api/v1/historical/{symbol}/status", params=params)

    def resample_data(self, symbol: str, market_type: str, start_date: datetime,
                     end_date: datetime, timeframe: str, include_indicators: bool = False) -> Dict[str, Any]:
        """Resample data to higher timeframes."""
        params = {
            "market_type": market_type,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "timeframe": timeframe,
            "include_indicators": include_indicators
        }
        return self._make_request("GET", f"/api/v1/resample/{symbol}", params=params)

    def get_available_timeframes(self) -> Dict[str, Any]:
        """Get available timeframes for resampling."""
        return self._make_request("GET", "/api/v1/resample/timeframes")
    
    def check_api_server(self) -> bool:
        """Check if API server is running."""
        try:
            health = self.health_check()
            if health.get("status") == "healthy":
                logger.info("✓ API server is healthy")
                return True
            else:
                logger.error("API server is not healthy")
                return False
        except Exception as e:
            logger.error(f"API server check failed: {e}")
            return False

    def initialize_services(self) -> bool:
        """Initialize services and check API connectivity."""
        try:
            # Validate configuration
            if not validate_configuration():
                logger.error("Configuration validation failed")
                return False

            # Check API server
            if not self.check_api_server():
                logger.error("API server not available")
                return False

            logger.info("✓ Services initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def get_symbols_to_load(self, symbol_filter: str = None, specific_symbols: List[str] = None,
                           market_type: str = "EQUITY") -> List[str]:
        """Get list of symbols to load data for."""
        try:
            # Handle specific symbols
            if specific_symbols:
                logger.info("📋 Using specific symbols from command line...")
                return specific_symbols

            # Get symbols based on filter
            if symbol_filter == "nifty50":
                symbols_data = self.get_symbols(market_type="EQUITY", limit=50)
            elif symbol_filter == "indices":
                symbols_data = self.get_symbols(market_type="INDEX", limit=20)
            elif symbol_filter == "all":
                symbols_data = self.get_symbols(market_type=market_type, limit=1000)
            else:
                # Default to major indices
                symbols_data = self.get_symbols(market_type="INDEX", limit=10)

            symbols = [s.get("symbol", s) if isinstance(s, dict) else s for s in symbols_data]
            logger.info(f"📋 Selected {len(symbols)} symbols for {symbol_filter or 'default'} filter")
            return symbols

        except Exception as e:
            logger.error(f"Error getting symbols to load: {e}")
            return []
    
    def load_symbol_data(self, symbol: str, market_type: str, start_date: datetime, end_date: datetime) -> bool:
        """Load historical data for a single symbol using API."""
        try:
            logger.info(f"🔄 Loading data for {symbol} ({market_type})...")

            # Get initial data summary
            try:
                initial_summary = self.get_data_summary(symbol, market_type)
                initial_count = initial_summary.get("total_records", 0)
                logger.info(f"   Initial database records: {initial_count:,}")
            except:
                initial_count = 0
                logger.info("   No existing data found")

            # Fetch historical data via API
            response = self.fetch_historical_data(symbol, market_type, start_date, end_date)

            if response.get("status") == "processing":
                logger.info(f"   📡 Historical data fetch started for {symbol}")

                # Poll for completion (simplified for demo)
                max_wait = 300  # 5 minutes
                wait_time = 0

                while wait_time < max_wait:
                    if self.interrupted:
                        logger.info("   ⚠️  Operation interrupted by user")
                        return False

                    time.sleep(10)  # Wait 10 seconds
                    wait_time += 10

                    try:
                        status = self.get_historical_status(symbol, market_type)
                        if status.get("status") == "COMPLETED":
                            logger.info(f"   ✅ {symbol}: Historical data fetch completed")
                            break
                        elif status.get("status") == "FAILED":
                            logger.error(f"   ❌ {symbol}: Historical data fetch failed")
                            return False
                    except:
                        continue

                # Get final data summary
                try:
                    final_summary = self.get_data_summary(symbol, market_type)
                    final_count = final_summary.get("total_records", 0)
                    new_records = final_count - initial_count

                    logger.info(f"   ✅ {symbol}: Successfully loaded {new_records:,} new records")
                    logger.info(f"   📊 Total database records: {final_count:,}")

                    # Display latest records
                    self.display_latest_records(symbol, market_type, count=5)
                    return True
                except:
                    logger.warning(f"   ⚠️  Could not get final summary for {symbol}")
                    return True  # Assume success if fetch completed
            else:
                logger.error(f"   ❌ {symbol}: Failed to start historical data fetch")
                return False

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            return False

    def display_latest_records(self, symbol: str, market_type: str, count: int = 5) -> None:
        """Display latest records for a symbol."""
        try:
            response = self.get_latest_data(symbol, market_type, count)
            data = response.get("data", [])

            if data:
                logger.info(f"   📊 Latest {len(data)} records for {symbol}:")
                for record in data[-count:]:
                    dt = record.get("datetime", "")
                    close = record.get("close", 0)
                    volume = record.get("volume", 0)
                    logger.info(f"      {dt}: Close={close}, Volume={volume:,}")
        except Exception as e:
            logger.warning(f"Could not display latest records for {symbol}: {e}")

    def run_bulk_load(self, symbols: List[str], market_type: str = "EQUITY", years: int = 15) -> Dict[str, bool]:
        """Run bulk data loading for multiple symbols using API."""
        try:
            end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = end_date - timedelta(days=years * 365)

            logger.info(f"🚀 Starting bulk data loading for {len(symbols)} symbols...")
            logger.info(f"Market type: {market_type}")
            logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
            logger.info("=" * 80)

            results = {}
            successful_loads = 0
            failed_loads = 0

            for i, symbol in enumerate(symbols, 1):
                if self.interrupted:
                    logger.info("⚠️  Operation interrupted by user")
                    break

                logger.info(f"\n📊 Processing symbol {i}/{len(symbols)}: {symbol}")

                success = self.load_symbol_data(symbol, market_type, start_date, end_date)
                results[symbol] = success

                if success:
                    successful_loads += 1
                else:
                    failed_loads += 1

                # Progress summary
                logger.info(f"   Progress: {i}/{len(symbols)} symbols processed")
                logger.info(f"   Success: {successful_loads}, Failed: {failed_loads}")

                # Rate limiting between symbols
                if i < len(symbols):
                    time.sleep(2)

            # Final summary
            logger.info("\n" + "=" * 80)
            logger.info("📈 BULK LOADING SUMMARY")
            logger.info("=" * 80)
            logger.info(f"Total symbols processed: {len(results)}")
            logger.info(f"Successful loads: {successful_loads}")
            logger.info(f"Failed loads: {failed_loads}")
            if len(results) > 0:
                logger.info(f"Success rate: {(successful_loads/len(results)*100):.1f}%")

            return results

        except Exception as e:
            logger.error(f"Error in bulk loading: {e}")
            return {}
    
    def display_data_summary(self, symbols: List[str], market_type: str = "EQUITY") -> None:
        """Display data availability summary for symbols using API."""
        try:
            logger.info("📅 DATA AVAILABILITY SUMMARY")
            logger.info("=" * 120)
            logger.info(f"{'Symbol':<15} {'Market Type':<12} {'First Record':<20} {'Last Record':<20} {'Total Records':<15}")
            logger.info("=" * 120)

            for symbol in symbols:
                if self.interrupted:
                    break

                try:
                    summary = self.get_data_summary(symbol, market_type)
                    if summary:
                        first_ts = summary.get('first_timestamp', 'N/A')
                        last_ts = summary.get('last_timestamp', 'N/A')
                        total_records = summary.get('total_records', 0)

                        # Format timestamps
                        if first_ts and first_ts != 'N/A':
                            first_date = first_ts[:10] if len(first_ts) > 10 else first_ts
                        else:
                            first_date = 'N/A'

                        if last_ts and last_ts != 'N/A':
                            last_date = last_ts[:10] if len(last_ts) > 10 else last_ts
                        else:
                            last_date = 'N/A'

                        logger.info(f"{symbol:<15} {market_type:<12} {first_date:<20} {last_date:<20} {total_records:<15,}")
                    else:
                        logger.info(f"{symbol:<15} {market_type:<12} {'No data found':<20} {'-':<20} {'-':<15}")
                except Exception as e:
                    logger.warning(f"Could not get summary for {symbol}: {e}")
                    logger.info(f"{symbol:<15} {market_type:<12} {'Error':<20} {'-':<20} {'-':<15}")

            logger.info("=" * 120)

        except Exception as e:
            logger.error(f"Error displaying data summary: {e}")

    def cleanup(self):
        """Cleanup resources."""
        if hasattr(self, 'session'):
            self.session.close()
            logger.info("✓ HTTP session closed")

def main():
    """Main execution function with real data operations."""
    parser = argparse.ArgumentParser(description="Robust Data Storage Service - Real Data Management")
    parser.add_argument("--years", type=int, default=1,
                       help="Number of years to load (default: 1)")
    parser.add_argument("--symbols", choices=["nifty50", "indices", "all", "equity", "futures", "options"],
                       default="nifty50", help="Symbol set to load (default: nifty50)")
    parser.add_argument("--market-type", choices=["EQUITY", "INDEX", "FUTURES", "OPTIONS"],
                       default="EQUITY", help="Market type (default: EQUITY)")
    parser.add_argument("--specific-symbols", nargs='+',
                       help="Specific symbols to load (e.g., --specific-symbols RELIANCE TCS INFY)")
    parser.add_argument("--bulk-insert", action="store_true",
                       help="Perform bulk insert of historical data from Fyers API")
    parser.add_argument("--view-data", action="store_true",
                       help="Display data availability summary for all market types")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show symbols that would be loaded without actually loading")
    parser.add_argument("--fetch-symbols", action="store_true",
                       help="Fetch and update symbol list from NSE")
    parser.add_argument("--start-date", type=str,
                       help="Start date for operations (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str,
                       help="End date for operations (YYYY-MM-DD)")
    parser.add_argument("--api", action="store_true",
                       help="Start API server")
    parser.add_argument("--init-db", action="store_true",
                       help="Initialize database schema")

    args = parser.parse_args()

    # Parse dates if provided
    start_date = None
    end_date = None
    if args.start_date:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
    if args.end_date:
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")

    if args.api:
        # Start API server
        logger.info("🚀 Starting Robust Data Storage Service API server...")
        from api.server import start_server
        start_server()
        return True

    if args.init_db:
        # Initialize database
        logger.info("🔧 Initializing database schema...")
        try:
            init_database()
            logger.info("✅ Database initialization completed")
            return True
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            return False

    logger.info("🚀 Robust Data Storage Service - Real Data Management")
    logger.info("=" * 80)

    try:
        # For bulk operations, work directly with database
        if args.bulk_insert or args.view_data:
            logger.info("Using direct database access for bulk operations...")

            # Check database connection
            if not check_database_connection():
                logger.error("❌ Database connection failed")
                return False
        else:
            # For other operations, use API client
            service = DataServiceClient()

            # Initialize services
            logger.info("Step 1: Initializing services...")
            if not service.initialize_services():
                logger.error("❌ Service initialization failed. Make sure API server is running.")
                logger.info("💡 Start the API server with: python main.py --api")
                return False

        # Handle symbol loading differently for bulk operations
        if args.bulk_insert or args.view_data:
            # For bulk operations, get symbols directly
            if args.specific_symbols:
                symbols = args.specific_symbols
                logger.info(f"Using specific symbols: {symbols}")
            else:
                # Default symbols for testing
                if args.market_type == "EQUITY":
                    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
                elif args.market_type == "INDEX":
                    symbols = ["NIFTY50", "BANKNIFTY", "NIFTYIT"]
                else:
                    symbols = ["RELIANCE", "TCS"]  # Default for futures/options
                logger.info(f"Using default {args.market_type} symbols: {symbols}")
        else:
            # Fetch symbols if requested
            if args.fetch_symbols:
                logger.info("\n🔄 Fetching symbols from NSE...")
                try:
                    result = service.fetch_nse_symbols(force_refresh=True)
                    logger.info(f"✅ Symbol fetch completed: {result}")
                    return True
                except Exception as e:
                    logger.error(f"❌ Symbol fetch failed: {e}")
                    return False

            # Get symbols to load
            logger.info("\nStep 2: Getting symbols to load...")
            symbols = service.get_symbols_to_load(
                symbol_filter=args.symbols,
                specific_symbols=args.specific_symbols,
                market_type=args.market_type
            )

        if not symbols:
            logger.error("No symbols found to load")
            return False

        if args.dry_run:
            logger.info(f"\n📋 DRY RUN - Would load data for {len(symbols)} symbols:")
            for i, symbol in enumerate(symbols, 1):
                logger.info(f"  {i:3d}. {symbol} ({args.market_type})")
            return True

        if args.view_data:
            logger.info(f"\n📅 Displaying data availability for all market types...")
            bulk_service = BulkDataService()

            for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                logger.info(f"\n{market_type.value} Data Summary:")
                summary = bulk_service.get_data_summary(market_type, symbols if args.specific_symbols else None)
                if 'error' in summary:
                    logger.error(f"  Error: {summary['error']}")
                else:
                    logger.info(f"  Total Records: {summary['total_records']:,}")
                    logger.info(f"  Symbols Count: {summary['symbols_count']}")
                    if summary['date_range']:
                        logger.info(f"  Date Range: {summary['date_range']['start']} to {summary['date_range']['end']}")
                    else:
                        logger.info("  No data available")
            return True

        # Handle bulk insert operation
        if args.bulk_insert:
            logger.info(f"\n� Starting bulk insert of real historical data from Fyers API")

            # Set date range
            if not start_date:
                end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                start_date = end_date - timedelta(days=args.years * 365)
            elif not end_date:
                end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

            logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"Market type: {args.market_type}")
            logger.info(f"Symbols: {len(symbols)}")

            # Initialize bulk data service
            bulk_service = BulkDataService()

            # Convert market type string to enum
            market_type_enum = MarketType(args.market_type)

            # Run bulk population
            import asyncio
            results = asyncio.run(bulk_service.populate_historical_data(
                symbols=symbols,
                market_type=market_type_enum,
                start_date=start_date,
                end_date=end_date
            ))

            # Show final results
            successful_symbols = [s for s, success in results.items() if success]
            failed_symbols = [s for s, success in results.items() if not success]

            if successful_symbols:
                logger.info(f"\n✅ Successfully populated data for {len(successful_symbols)} symbols")
                for symbol in successful_symbols[:10]:  # Show first 10
                    logger.info(f"   ✓ {symbol}")
                if len(successful_symbols) > 10:
                    logger.info(f"   ... and {len(successful_symbols) - 10} more")

            if failed_symbols:
                logger.info(f"\n❌ Failed to populate data for {len(failed_symbols)} symbols:")
                for symbol in failed_symbols:
                    logger.info(f"   ✗ {symbol}")

            return len(failed_symbols) == 0

        # Default behavior - show help
        logger.info("\n💡 Use --bulk-insert to populate historical data from Fyers API")
        logger.info("💡 Use --view-data to see data availability summary")
        logger.info("💡 Use --specific-symbols to work with specific symbols")
        logger.info("💡 Use --help for all available options")
        return True

    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        # Only cleanup service if it was initialized
        if 'service' in locals():
            service.cleanup()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)