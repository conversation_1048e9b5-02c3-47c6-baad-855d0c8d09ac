#!/usr/bin/env python3
"""
Migrate database tables to new structure.
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from database.connection import get_db
from sqlalchemy import text

def migrate_tables():
    """Drop and recreate tables with correct structure."""
    db = next(get_db())
    
    try:
        print("Dropping existing OHLCV tables...")
        
        # Drop existing tables
        tables_to_drop = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']
        
        for table in tables_to_drop:
            try:
                db.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE"))
                print(f"  Dropped {table}")
            except Exception as e:
                print(f"  Warning dropping {table}: {e}")
        
        db.commit()
        
        print("\nCreating enum types...")

        # Create option_type enum if it doesn't exist
        db.execute(text("""
            DO $$ BEGIN
                CREATE TYPE option_type AS ENUM ('CE', 'PE');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """))
        print("  Created option_type enum")

        print("\nCreating new tables with correct structure...")

        # Create equity_ohlcv
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS equity_ohlcv (
                symbol text COLLATE pg_catalog."default" NOT NULL,
                exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
                "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
                datetime timestamp without time zone NOT NULL,
                open double precision NOT NULL,
                high double precision NOT NULL,
                low double precision NOT NULL,
                close double precision NOT NULL,
                volume bigint NOT NULL,
                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
                CONSTRAINT equity_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime)
            )
        """))
        print("  Created equity_ohlcv")
        
        # Create index_ohlcv
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS index_ohlcv (
                symbol text COLLATE pg_catalog."default" NOT NULL,
                exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
                "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
                datetime timestamp without time zone NOT NULL,
                open double precision NOT NULL,
                high double precision NOT NULL,
                low double precision NOT NULL,
                close double precision NOT NULL,
                volume bigint NOT NULL,
                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
                CONSTRAINT index_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime)
            )
        """))
        print("  Created index_ohlcv")
        
        # Create futures_ohlcv
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS futures_ohlcv (
                symbol text COLLATE pg_catalog."default" NOT NULL,
                exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
                "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
                datetime timestamp without time zone NOT NULL,
                open double precision NOT NULL,
                high double precision NOT NULL,
                low double precision NOT NULL,
                close double precision NOT NULL,
                volume bigint NOT NULL,
                expiry_date date NOT NULL,
                open_interest bigint DEFAULT 0,
                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
                CONSTRAINT futures_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime, expiry_date)
            )
        """))
        print("  Created futures_ohlcv")
        
        # Create options_ohlcv
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS options_ohlcv (
                symbol text COLLATE pg_catalog."default" NOT NULL,
                exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
                "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
                datetime timestamp without time zone NOT NULL,
                open double precision NOT NULL,
                high double precision NOT NULL,
                low double precision NOT NULL,
                close double precision NOT NULL,
                volume bigint NOT NULL,
                expiry_date date NOT NULL,
                strike_price double precision NOT NULL,
                option_type option_type NOT NULL,
                open_interest bigint DEFAULT 0,
                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
                CONSTRAINT options_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime, expiry_date, strike_price, option_type)
            )
        """))
        print("  Created options_ohlcv")
        
        # Create indexes
        print("\nCreating indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_equity_symbol_datetime ON equity_ohlcv (symbol, datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_equity_datetime_desc ON equity_ohlcv (datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_index_symbol_datetime ON index_ohlcv (symbol, datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_index_datetime_desc ON index_ohlcv (datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_futures_symbol_datetime ON futures_ohlcv (symbol, datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_futures_expiry_datetime ON futures_ohlcv (expiry_date, datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_futures_datetime_desc ON futures_ohlcv (datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_options_symbol_datetime ON options_ohlcv (symbol, datetime DESC)",
            "CREATE INDEX IF NOT EXISTS idx_options_expiry_strike ON options_ohlcv (expiry_date, strike_price, option_type)",
            "CREATE INDEX IF NOT EXISTS idx_options_datetime_desc ON options_ohlcv (datetime DESC)"
        ]
        
        for idx in indexes:
            try:
                db.execute(text(idx))
                print(f"  Created index")
            except Exception as e:
                print(f"  Warning creating index: {e}")
        
        db.commit()
        print("\n✅ Migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    migrate_tables()
