#!/usr/bin/env python3
"""
Test script for API endpoints.
Tests all major API endpoints for data retrieval and storage operations.
"""

import sys
import os
import requests
import json
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from core.config import settings
from core.logging import get_logger

logger = get_logger(__name__)

class APITester:
    """API endpoint tester."""
    
    def __init__(self, base_url: str = None):
        """Initialize API tester."""
        self.base_url = base_url or f"http://{settings.api.host}:{settings.api.port}"
        self.session = requests.Session()
        
    def test_health_endpoint(self) -> bool:
        """Test health endpoint."""
        logger.info("🔍 Testing health endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            data = response.json()
            if data.get("status") == "healthy":
                logger.info("✅ Health endpoint: PASSED")
                logger.info(f"   Database: {data.get('database', 'unknown')}")
                logger.info(f"   TimescaleDB: {data.get('timescaledb', 'unknown')}")
                return True
            else:
                logger.error(f"❌ Health endpoint: FAILED - {data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Health endpoint: FAILED - {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """Test root endpoint."""
        logger.info("🔍 Testing root endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            response.raise_for_status()
            
            data = response.json()
            if "message" in data and "endpoints" in data:
                logger.info("✅ Root endpoint: PASSED")
                logger.info(f"   Version: {data.get('version', 'unknown')}")
                return True
            else:
                logger.error(f"❌ Root endpoint: FAILED - {data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Root endpoint: FAILED - {e}")
            return False
    
    def test_data_summary_endpoint(self) -> bool:
        """Test data summary endpoint."""
        logger.info("🔍 Testing data summary endpoints...")
        
        test_cases = [
            ("RELIANCE", "EQUITY"),
            ("NIFTY50", "INDEX"),
            ("RELIANCE", "FUTURES"),
            ("RELIANCE", "OPTIONS")
        ]
        
        passed = 0
        for symbol, market_type in test_cases:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/v1/data/{symbol}/summary",
                    params={"market_type": market_type}
                )
                response.raise_for_status()
                
                data = response.json()
                if "total_records" in data:
                    logger.info(f"✅ Summary {symbol} ({market_type}): {data['total_records']} records")
                    passed += 1
                else:
                    logger.warning(f"⚠️  Summary {symbol} ({market_type}): No data found")
                    
            except Exception as e:
                logger.error(f"❌ Summary {symbol} ({market_type}): FAILED - {e}")
        
        success = passed > 0
        logger.info(f"✅ Data summary endpoints: {passed}/{len(test_cases)} passed")
        return success
    
    def test_latest_data_endpoint(self) -> bool:
        """Test latest data endpoint."""
        logger.info("🔍 Testing latest data endpoints...")
        
        test_cases = [
            ("RELIANCE", "EQUITY"),
            ("NIFTY50", "INDEX"),
            ("RELIANCE", "FUTURES"),
            ("RELIANCE", "OPTIONS")
        ]
        
        passed = 0
        for symbol, market_type in test_cases:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/v1/data/{symbol}/latest",
                    params={"market_type": market_type, "count": 3}
                )
                response.raise_for_status()
                
                data = response.json()
                if "data" in data and len(data["data"]) > 0:
                    logger.info(f"✅ Latest {symbol} ({market_type}): {len(data['data'])} records")
                    
                    # Show sample record
                    sample = data["data"][-1]
                    logger.info(f"   Sample: {sample.get('datetime', 'N/A')} - Close: {sample.get('close', 'N/A')}")
                    passed += 1
                else:
                    logger.warning(f"⚠️  Latest {symbol} ({market_type}): No data found")
                    
            except Exception as e:
                logger.error(f"❌ Latest {symbol} ({market_type}): FAILED - {e}")
        
        success = passed > 0
        logger.info(f"✅ Latest data endpoints: {passed}/{len(test_cases)} passed")
        return success
    
    def test_data_range_endpoint(self) -> bool:
        """Test data range endpoint."""
        logger.info("🔍 Testing data range endpoints...")
        
        # Use a small date range around our test data
        start_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(hours=1)
        
        test_cases = [
            ("RELIANCE", "EQUITY"),
            ("NIFTY50", "INDEX")
        ]
        
        passed = 0
        for symbol, market_type in test_cases:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/v1/data/{symbol}/range",
                    params={
                        "market_type": market_type,
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "limit": 10
                    }
                )
                response.raise_for_status()
                
                data = response.json()
                if "data" in data and len(data["data"]) > 0:
                    logger.info(f"✅ Range {symbol} ({market_type}): {len(data['data'])} records")
                    passed += 1
                else:
                    logger.warning(f"⚠️  Range {symbol} ({market_type}): No data in range")
                    
            except Exception as e:
                logger.error(f"❌ Range {symbol} ({market_type}): FAILED - {e}")
        
        success = passed > 0
        logger.info(f"✅ Data range endpoints: {passed}/{len(test_cases)} passed")
        return success
    
    def test_bulk_insert_endpoint(self) -> bool:
        """Test bulk insert endpoint."""
        logger.info("🔍 Testing bulk insert endpoint...")
        
        try:
            # Prepare test data
            test_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
            test_data = {
                "market_type": "EQUITY",
                "data": [
                    {
                        "datetime": test_time.isoformat(),
                        "symbol": "TESTSTOCK",
                        "open": 1000.0,
                        "high": 1010.0,
                        "low": 995.0,
                        "close": 1005.0,
                        "volume": 50000
                    }
                ]
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/data/bulk-insert",
                json=test_data
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get("inserted", 0) > 0:
                logger.info(f"✅ Bulk insert: PASSED - {data['inserted']} records inserted")
                return True
            else:
                logger.warning(f"⚠️  Bulk insert: No records inserted - {data}")
                return True  # Still consider it a pass if no error
                
        except Exception as e:
            logger.error(f"❌ Bulk insert: FAILED - {e}")
            return False
    
    def test_symbols_endpoint(self) -> bool:
        """Test symbols endpoint."""
        logger.info("🔍 Testing symbols endpoints...")
        
        try:
            # Test get all symbols
            response = self.session.get(
                f"{self.base_url}/api/v1/symbols/",
                params={"limit": 10}
            )
            response.raise_for_status()
            
            data = response.json()
            if "symbols" in data:
                logger.info(f"✅ Symbols list: PASSED - {len(data['symbols'])} symbols")
                
                # Test specific symbol info if we have symbols
                if data["symbols"]:
                    symbol = data["symbols"][0]
                    symbol_name = symbol if isinstance(symbol, str) else symbol.get("symbol", "RELIANCE")
                    
                    try:
                        response = self.session.get(f"{self.base_url}/api/v1/symbols/{symbol_name}")
                        if response.status_code == 200:
                            logger.info(f"✅ Symbol info ({symbol_name}): PASSED")
                        else:
                            logger.warning(f"⚠️  Symbol info ({symbol_name}): Not found")
                    except:
                        logger.warning(f"⚠️  Symbol info ({symbol_name}): Error")
                
                return True
            else:
                logger.error(f"❌ Symbols list: FAILED - {data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Symbols endpoints: FAILED - {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all API tests."""
        logger.info("🚀 Starting API Endpoint Tests")
        logger.info("=" * 80)
        
        tests = [
            ("Health Check", self.test_health_endpoint),
            ("Root Endpoint", self.test_root_endpoint),
            ("Data Summary", self.test_data_summary_endpoint),
            ("Latest Data", self.test_latest_data_endpoint),
            ("Data Range", self.test_data_range_endpoint),
            ("Bulk Insert", self.test_bulk_insert_endpoint),
            ("Symbols", self.test_symbols_endpoint)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 Running {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        # Summary
        logger.info("\n" + "=" * 80)
        logger.info("📊 API ENDPOINT TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {total - passed}")
        logger.info(f"Success rate: {(passed/total*100):.1f}%")
        
        if passed == total:
            logger.info("🎉 ALL API TESTS PASSED!")
        elif passed > total // 2:
            logger.info("✅ Most API tests passed - system is functional")
        else:
            logger.error("❌ Many API tests failed - check system configuration")
        
        logger.info("=" * 80)
        
        return passed > total // 2

def main():
    """Main test function."""
    logger.info("🚀 API Endpoint Testing")
    logger.info("=" * 80)
    
    # Check if API server is running by trying to connect
    tester = APITester()
    
    try:
        # Quick connectivity test
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        logger.info(f"✅ API server is running at {tester.base_url}")
    except requests.exceptions.ConnectionError:
        logger.error(f"❌ API server is not running at {tester.base_url}")
        logger.info("💡 Start the API server with: python -m uvicorn src.api.server:app --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        logger.error(f"❌ Error connecting to API server: {e}")
        return False
    
    # Run all tests
    return tester.run_all_tests()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
