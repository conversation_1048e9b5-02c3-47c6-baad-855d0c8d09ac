#!/usr/bin/env python3
"""
Check database tables structure.
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from database.connection import get_db
from sqlalchemy import text

def check_tables():
    """Check existing tables and their structure."""
    db = next(get_db())
    
    try:
        # Get all OHLCV tables
        result = db.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%ohlcv'
            ORDER BY table_name
        """))
        
        tables = [row[0] for row in result]
        print("Existing OHLCV tables:")
        for table in tables:
            print(f"  - {table}")
        
        # Check structure of each table
        for table in tables:
            print(f"\nStructure of {table}:")
            result = db.execute(text(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = '{table}'
                ORDER BY ordinal_position
            """))
            
            for row in result:
                print(f"  {row[0]}: {row[1]} {'NULL' if row[2] == 'YES' else 'NOT NULL'} {row[3] or ''}")
                
    except Exception as e:
        print(f"Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_tables()
